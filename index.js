const axios = require('axios');
const cheerio = require('cheerio');
const fs = require('fs');
const path = require('path');


// File paths for storing data
const jsonFilePath = path.join(__dirname, 'products.json');
const csvFilePath = path.join(__dirname, 'products.csv');


// Cache to store fetched pages
const cache = {};


// Function to fetch HTML with caching
async function fetchHTML(url) {
 if (cache[url]) {
   console.log(`Fetching from cache: ${url}`);
   return cache[url];
 }


 try {
   const { data } = await axios.get(url);
   cache[url] = data;
   console.log(`Fetched from network: ${url}`);
   return data;
 } catch (error) {
   console.error('Error fetching the HTML:', error);
   throw error;
 }
}


// Function to extract product data
function extractProducts(html) {
 const $ = cheerio.load(html);
 const products = [];


 $('.product-card').each((index, element) => {
   const name = $(element).find('.title').text().trim();
   const price = $(element).find('.price-wrapper').text().trim();
   const description = $(element).find('.description').text().trim();
   const stock = $(element).find('.in-stock')
   products.push({ name, price, description, stock});
 });

 return products;
}


// Function to save data to JSON file
function saveToJSON(data) {
 fs.writeFileSync(jsonFilePath, JSON.stringify(data, null, 2));
 console.log(`Data saved to products.json`);
}


// Function to save data to CSV file
function saveToCSV(data) {
 const csvHeader = 'Name,Price\n';
 const csvRows = data.map(product => `${product.name.replace(/,/g, '')},${product.price}`).join('\n');
 fs.writeFileSync(csvFilePath, csvHeader + csvRows);
 console.log(`Data saved to products.csv`);
}


// Function to scrape products from multiple pages
async function scrapeProducts(url, allProducts = []) {
 try {
   const html = await fetchHTML(url);
   const products = extractProducts(html);
   allProducts = allProducts.concat(products);

   console.log(allProducts);
   // Save data to CSV and JSON files
   //saveToJSON(allProducts);
   //saveToCSV(allProducts);


   // Check if there is a next page
   //const $ = cheerio.load(html);
   //const nextPage = $('.next > a').attr('href');
   //if (nextPage.indexOf("#") == -1) {
     //const nextUrl = new URL(nextPage, url).href;
     //await scrapeProducts(nextUrl, allProducts);
   //}
 } catch (error) {
   console.error('Failed to scrape the website:', error);
 }
}


(async () => {
 const baseURL = 'https://sandbox.oxylabs.io/products';
 await scrapeProducts(baseURL);
})();